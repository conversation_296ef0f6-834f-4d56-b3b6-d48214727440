import { Request, Response } from "express";
import logger from "../utils/logger";
import { BedrockService } from "../services/BedrockService";
import { AiChatResponse, AiErrorCode } from "../types/ai";
import { AiError } from "../middleware/aiErrorHandler";

export class AiController {
  private bedrockService: BedrockService;

  constructor() {
    this.bedrockService = new BedrockService();
    // 绑定方法到实例
    this.chat = this.chat.bind(this);
  }

  /**
   * 处理AI对话请求
   * @param req Request对象，包含用户输入和canvas状态
   * @param res Response对象
   */
  async chat(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();

    try {
      const { userInput, canvasState } = req.body;

      logger.info(`AI对话请求 - 用户输入: ${userInput.substring(0, 100)}...`);
      logger.info(`画布状态包含 ${canvasState.elements?.length || 0} 个元素`);

      let aiResponse: AiChatResponse;

      // 检查Bedrock服务是否配置
      if (!this.bedrockService.isConfigured()) {
        logger.warn("AWS Bedrock未配置，使用模拟响应");
        const mockResponse = this.generateMockResponse(userInput, canvasState);
        aiResponse = {
          response: mockResponse,
          timestamp: new Date().toISOString(),
          metadata: {
            elementCount: canvasState.elements?.length || 0,
            canvasSize: `${canvasState.width}x${canvasState.height}`,
            processingTime: Date.now() - startTime,
          },
        };
      } else {
        // 调用Bedrock服务
        aiResponse = await this.bedrockService.chat({
          userInput,
          canvasState,
        });

        // 添加元数据
        aiResponse.metadata = {
          elementCount: canvasState.elements?.length || 0,
          canvasSize: `${canvasState.width}x${canvasState.height}`,
          processingTime: Date.now() - startTime,
        };
      }

      logger.info(`AI对话完成，处理时间: ${Date.now() - startTime}ms`);
      res.json(aiResponse);
    } catch (error) {
      logger.error("AI对话处理错误:", error);

      // 抛出错误，让错误处理中间件处理
      if (error instanceof AiError) {
        throw error;
      }

      throw new AiError(
        "AI服务处理失败",
        AiErrorCode.INTERNAL_ERROR,
        500,
        error instanceof Error ? error.message : "未知错误"
      );
    }
  }

  /**
   * 生成模拟AI响应（临时使用，后续将替换为Bedrock调用）
   * @param userInput 用户输入
   * @param canvasState 画布状态
   * @returns 模拟响应
   */
  private generateMockResponse(userInput: string, canvasState: any): string {
    const elementCount = canvasState.elements?.length || 0;
    const canvasSize = `${canvasState.width}x${canvasState.height}`;

    // 简单的关键词匹配响应
    const input = userInput.toLowerCase();

    if (input.includes("帮助") || input.includes("help")) {
      return `我是您的AI视频编辑助手！我可以帮您：
1. 分析当前项目（您的画布有${elementCount}个元素，尺寸为${canvasSize}）
2. 提供编辑建议
3. 回答视频制作相关问题
4. 优化您的视频内容

请告诉我您需要什么帮助！`;
    }

    if (input.includes("元素") || input.includes("element")) {
      return `您的项目当前包含${elementCount}个元素。画布尺寸为${canvasSize}。${
        elementCount > 0
          ? "我可以帮您分析这些元素的布局和时间安排。"
          : "您可以开始添加视频、图片、文本或音频元素。"
      }`;
    }

    if (
      input.includes("建议") ||
      input.includes("优化") ||
      input.includes("improve")
    ) {
      return `基于您当前的项目分析：
- 画布尺寸：${canvasSize}
- 元素数量：${elementCount}个

${
  elementCount === 0
    ? "建议先添加一些基础元素，比如背景图片或视频。"
    : elementCount < 3
    ? "可以考虑添加更多元素来丰富内容，比如文字说明或背景音乐。"
    : "项目看起来很丰富！建议检查元素之间的时间安排和视觉层次。"
}`;
    }

    return `感谢您的问题："${userInput}"。我正在分析您的项目（${elementCount}个元素，${canvasSize}画布）。作为AI助手，我可以帮您优化视频编辑流程。请告诉我您具体需要什么帮助？`;
  }
}
