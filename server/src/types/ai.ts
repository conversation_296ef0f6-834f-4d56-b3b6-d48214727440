/**
 * AI对话相关的类型定义
 */

export interface AiChatRequest {
  userInput: string;
  canvasState: CanvasStateForAI;
}

export interface AiChatResponse {
  response: string;
  timestamp: string;
  metadata?: {
    elementCount: number;
    canvasSize: string;
    processingTime: number;
  };
}

export interface CanvasStateForAI {
  width: number;
  height: number;
  backgroundColor: string;
  elements: ElementForAI[];
  animations?: AnimationForAI[];
  captions?: CaptionForAI[];
  tracks?: TrackForAI[];
}

export interface ElementForAI {
  id: string;
  type: "video" | "image" | "audio" | "text" | "shape";
  properties: Record<string, any>;
  placement?: PlacementForAI;
  timeFrame: TimeFrameForAI;
  opacity?: number;
}

export interface PlacementForAI {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  scaleX?: number;
  scaleY?: number;
}

export interface TimeFrameForAI {
  start: number;
  end: number;
}

export interface AnimationForAI {
  id: string;
  type: string;
  duration: number;
  elementId: string;
}

export interface CaptionForAI {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
}

export interface TrackForAI {
  id: string;
  type: string;
  elementIds: string[];
}

export interface AiErrorResponse {
  error: string;
  details?: string;
  code?: string;
  timestamp: string;
}

export interface AiServiceConfig {
  modelId: string;
  maxTokens: number;
  temperature?: number;
  region: string;
}

// 错误类型枚举
export enum AiErrorCode {
  INVALID_INPUT = "INVALID_INPUT",
  MISSING_CANVAS_STATE = "MISSING_CANVAS_STATE",
  AWS_CREDENTIALS_ERROR = "AWS_CREDENTIALS_ERROR",
  BEDROCK_API_ERROR = "BEDROCK_API_ERROR",
  INTERNAL_ERROR = "INTERNAL_ERROR",
  SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
}

// AI响应状态枚举
export enum AiResponseStatus {
  SUCCESS = "SUCCESS",
  ERROR = "ERROR",
  PARTIAL = "PARTIAL",
}
