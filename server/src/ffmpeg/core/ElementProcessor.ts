import { FFmpegUtils } from "../utils";
import { TextFilterGenerator } from "./filters/TextFilterGenerator";
import { ImageFilterGenerator } from "./filters/ImageFilterGenerator";
import { VideoFilterGenerator } from "./filters/VideoFilterGenerator";
import { AudioFilterGenerator } from "./filters/AudioFilterGenerator";
import { MediaOverlayFilterGenerator } from "./filters/MediaOverlayFilterGenerator";
import { ShapeFilterGenerator } from "./filters/ShapeFilterGenerator";
import { MediaElement } from "../types";
import { ProcessingContext, ProcessingResult } from "./types";
import { BaseProcessor } from "./BaseProcessor";
import { TextElementASSUtils } from "../utils/textElementASSUtils";
import { Caption, CaptionStyle } from "../types";
import * as path from "path";
import * as os from "os";

/**
 * ElementProcessor handles the conversion of different media elements into FFmpeg filter commands.
 * It processes audio, video, image, and text elements to generate appropriate filters
 * for FFmpeg's complex filter system, allowing for the composition of multimedia content.
 */
export class ElementProcessor extends BaseProcessor {
  private readonly textFilterGenerator: TextFilterGenerator;
  private readonly imageFilterGenerator: ImageFilterGenerator;
  private readonly videoFilterGenerator: VideoFilterGenerator;
  private readonly audioFilterGenerator: AudioFilterGenerator;
  private readonly mediaOverlayFilterGenerator: MediaOverlayFilterGenerator;
  private readonly shapeFilterGenerator: ShapeFilterGenerator;

  constructor() {
    super();
    this.textFilterGenerator = new TextFilterGenerator();
    this.imageFilterGenerator = new ImageFilterGenerator();
    this.videoFilterGenerator = new VideoFilterGenerator();
    this.audioFilterGenerator = new AudioFilterGenerator();
    this.mediaOverlayFilterGenerator = new MediaOverlayFilterGenerator(
      this.textFilterGenerator
    );
    this.shapeFilterGenerator = new ShapeFilterGenerator();
  }

  /**
   * Process a media element based on its type
   * @param element - The media element to process
   * @param context - The processing context
   * @returns The processing result
   */
  protected processElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    switch (element.type) {
      case "audio":
        return this.processAudioElement(element, context);
      case "video":
        return this.processVideoElement(element, context);
      case "image":
        return this.processImageElement(element, context);
      case "text":
        return this.processTextElement(element, context);
      case "shape":
        return this.processShapeElement(element, context);
      default:
        throw new Error(`Unsupported element type: ${element.type}`);
    }
  }

  /**
   * Process audio element to add it to the FFmpeg command
   * 优化版本：处理音频元素，支持重用相同的输入源
   *
   * @param element - The audio element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the last video label and updated audio stream count
   */
  processAudioElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    if (!context.hasAudioContent) {
      return {
        lastVideoLabel: context.lastVideoLabel || "bg",
        audioStreamCount: context.audioStreamCount,
      };
    }

    // 添加媒体输入源，如果是相同的源会自动重用
    const audioInputIndex = this.addMediaInput(context.inputs, element);

    // 获取mediaStartTime属性，用于音频同步
    const mediaStartTime = element.properties.mediaStartTime
      ? Number(element.properties.mediaStartTime)
      : 0;

    console.log(
      `处理音频元素 ${element.id}，使用输入源索引: ${audioInputIndex}，mediaStartTime: ${mediaStartTime}`
    );

    const audioFilter = this.audioFilterGenerator.generateFilter(
      audioInputIndex,
      element,
      context.startTime,
      context.elementDuration,
      context.index,
      context.duration,
      context.audioStreamCount
    );

    // 直接添加音频过滤器，现在它已经包含输出标签[a${audioStreamCount}]
    context.audioFilters.push(audioFilter);

    return {
      lastVideoLabel: context.lastVideoLabel || "bg",
      audioStreamCount: context.audioStreamCount + 1,
    };
  }

  /**
   * Process video element to add it to the FFmpeg command
   * 优化版本：处理视频元素，支持重用相同的输入源
   *
   * @param element - The video element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the new last video label and updated audio stream count
   */
  processVideoElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    // 添加媒体输入源，如果是相同的源会自动重用
    const mediaInputIndex = this.addMediaInput(context.inputs, element);
    console.log(
      `处理视频元素 ${element.id}，使用输入源索引: ${mediaInputIndex}`
    );

    // 处理视频元素的音频部分
    if (
      !context.skipAudioProcessing &&
      context.audioElements?.[context.index] &&
      context.hasAudioContent
    ) {
      // 获取mediaStartTime属性，用于音频同步
      const mediaStartTime = element.properties.mediaStartTime
        ? Number(element.properties.mediaStartTime)
        : 0;

      console.log(
        `处理视频元素 ${element.id} 的音频部分，mediaStartTime: ${mediaStartTime}`
      );

      // 使用相同的输入源索引处理音频
      const audioFilter = this.audioFilterGenerator.generateFilter(
        mediaInputIndex,
        element,
        context.startTime,
        context.elementDuration,
        context.duration,
        context.audioStreamCount
      );

      // 确保音频过滤器有输出标签
      if (!audioFilter.endsWith(`[a${context.audioStreamCount}]`)) {
        context.audioFilters.push(
          `${audioFilter}[a${context.audioStreamCount}]`
        );
      } else {
        context.audioFilters.push(audioFilter);
      }

      context.audioStreamCount++;
    }

    // 处理视频部分
    const videoFilter = this.videoFilterGenerator.generateFilter(
      mediaInputIndex,
      element,
      context.elementDuration,
      context.index
    );

    // VideoFilterGenerator已经正确处理了mediaStartTime和setpts
    // 不需要在这里再次修改，直接使用生成的滤镜
    context.filterComplex.push(videoFilter);

    this.applyOverlay(
      context.filterComplex,
      element,
      context.index,
      context.canvasWidth,
      context.canvasHeight,
      context.duration,
      "vid",
      context.lastVideoLabel
    );

    return {
      lastVideoLabel: `v${context.index}`,
      audioStreamCount: context.audioStreamCount,
    };
  }

  /**
   * Process image element to add it to the FFmpeg command
   * 优化版本：处理图片元素，支持重用相同的输入源
   *
   * @param element - The image element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the new last video label and audio stream count (0 for images)
   */
  processImageElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    this.validateElement(element, context.index);

    // 添加媒体输入源，如果是相同的源会自动重用
    const mediaInputIndex = this.addMediaInput(context.inputs, element);
    console.log(
      `处理图片元素 ${element.id}，使用输入源索引: ${mediaInputIndex}`
    );

    const imageFilter = this.imageFilterGenerator.generateFilter(
      mediaInputIndex,
      element,
      context.startTime,
      context.duration,
      context.index
    );

    context.filterComplex.push(imageFilter);

    this.applyOverlay(
      context.filterComplex,
      element,
      context.index,
      context.canvasWidth,
      context.canvasHeight,
      context.duration,
      "img",
      context.lastVideoLabel
    );

    return {
      lastVideoLabel: `v${context.index}`,
      audioStreamCount: context.audioStreamCount,
    };
  }

  /**
   * Process text element to add it to the FFmpeg command using ASS subtitles
   *
   * @param element - The text element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the new last video label and audio stream count (0 for text)
   */
  processTextElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    // 使用ASS字幕方式处理文字元素，而不是drawtext滤镜
    this.applyTextElementAsASS(
      context.filterComplex,
      element,
      context.index,
      context.canvasWidth,
      context.canvasHeight,
      context.duration,
      context.lastVideoLabel
    );

    return {
      lastVideoLabel: `v${context.index}`,
      audioStreamCount: context.audioStreamCount,
    };
  }

  /**
   * Process shape element to add it to the FFmpeg command
   * Note: This method throws an error because shape processing requires async operations
   * Use processShapeElementAsync instead
   *
   * @param element - The shape element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Object containing the new last video label and audio stream count (0 for shapes)
   */
  processShapeElement(
    element: MediaElement,
    context: ProcessingContext
  ): ProcessingResult {
    throw new Error(
      "Shape elements require async processing. Use processShapeElementAsync instead."
    );
  }

  /**
   * Process shape element asynchronously to add it to the FFmpeg command
   * Uses ImageMagick to generate shape images, then treats them as image overlays
   *
   * @param element - The shape element to process with properties and placement data
   * @param context - The processing context containing necessary information
   * @returns Promise resolving to object containing the new last video label and audio stream count
   */
  async processShapeElementAsync(
    element: MediaElement,
    context: ProcessingContext
  ): Promise<ProcessingResult> {
    try {
      this.validateElement(element, context.index);

      console.log(`处理shape元素 ${element.id}，索引: ${context.index}`);

      // 使用ShapeFilterGenerator异步生成shape图片和滤镜
      const { filterString, imageInputPath } =
        await this.shapeFilterGenerator.generateShapeFilter(
          element as any, // 类型转换，因为shape元素结构与CanvasState元素兼容
          context.index
        );

      // 将生成的图片添加为输入源
      const imageInputIndex = context.inputs.length;
      context.inputs.push(`-i "${imageInputPath}"`);

      console.log(
        `Shape图片已添加为输入源 ${imageInputIndex}: ${imageInputPath}`
      );

      // 修改滤镜字符串以使用正确的输入索引
      const updatedFilterString = filterString.replace(
        /\[(\d+):v\]/g,
        `[${imageInputIndex}:v]`
      );

      // 应用叠加效果，类似图片元素的处理
      this.applyShapeOverlay(
        context.filterComplex,
        element,
        context.index,
        context.canvasWidth,
        context.canvasHeight,
        context.duration,
        context.lastVideoLabel,
        imageInputIndex,
        updatedFilterString
      );

      return {
        lastVideoLabel: `v${context.index}`,
        audioStreamCount: context.audioStreamCount,
      };
    } catch (error) {
      console.error(`处理shape元素失败:`, error);
      throw new Error(
        `处理shape元素失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`
      );
    }
  }

  /**
   * Apply shape overlay effect to combine the shape element with the existing video stream
   *
   * @param filterComplex - Array of complex filters to be modified
   * @param element - The shape element to overlay
   * @param index - Current element index in the timeline
   * @param canvasWidth - Width of the output canvas in pixels
   * @param canvasHeight - Height of the output canvas in pixels
   * @param duration - Total duration of the output video
   * @param lastVideoLabel - Label of the last processed video element
   * @param imageInputIndex - Index of the shape image input
   * @param shapeFilterString - Pre-generated shape filter string
   */
  private applyShapeOverlay(
    filterComplex: string[],
    element: MediaElement,
    index: number,
    canvasWidth: number,
    canvasHeight: number,
    duration: number,
    lastVideoLabel: string,
    imageInputIndex: number,
    shapeFilterString: string
  ): void {
    // 添加shape图片的处理滤镜
    filterComplex.push(shapeFilterString);

    // 生成叠加滤镜，类似图片元素的处理
    const overlayFilters =
      this.mediaOverlayFilterGenerator.generateMediaOverlayFilters(
        element,
        index,
        canvasWidth,
        canvasHeight,
        duration,
        "img" // 将shape当作图片处理
      );

    // 添加所有生成的叠加滤镜
    filterComplex.push(...overlayFilters);

    // 添加最终的叠加滤镜，将shape与背景合成
    const validVideoLabel = lastVideoLabel === `v-1` ? "bg" : lastVideoLabel;

    // 计算元素的显示时间范围（秒）
    const startTime = Number((element.timeFrame.start / 1000).toFixed(2));
    const endTime = Number((element.timeFrame.end / 1000).toFixed(2));

    // 添加enable参数来精确控制元素的显示时间
    const enableCondition = `enable='between(t,${startTime},${endTime})'`;

    // 如果元素有透明度设置，需要在overlay中应用
    let overlayFilter = `[${validVideoLabel}][img_timed${index}]overlay=format=auto:${enableCondition}`;

    // 添加透明度支持
    if (element.opacity !== undefined && element.opacity < 1) {
      // 在overlay之前先对图片应用透明度
      const alphaValue = element.opacity;
      filterComplex.push(
        `[img_timed${index}]format=rgba,colorchannelmixer=aa=${alphaValue}[img_alpha${index}]`
      );
      overlayFilter = `[${validVideoLabel}][img_alpha${index}]overlay=format=auto:${enableCondition}`;
    }

    filterComplex.push(`${overlayFilter},format=yuv420p[v${index}]`);
  }

  /**
   * 使用ASS字幕方式处理文字元素
   * 将文字元素转换为ASS字幕文件，然后应用到视频流
   * 支持动画效果，确保与前端动画效果一致
   *
   * @param filterComplex - 复杂滤镜数组
   * @param element - 文字元素
   * @param index - 元素索引
   * @param canvasWidth - 画布宽度
   * @param canvasHeight - 画布高度
   * @param duration - 视频总时长
   * @param lastVideoLabel - 上一个视频标签
   */
  private applyTextElementAsASS(
    filterComplex: string[],
    element: MediaElement,
    index: number,
    canvasWidth: number,
    canvasHeight: number,
    duration: number,
    lastVideoLabel: string
  ): void {
    try {
      // 将文字元素转换为Caption格式
      const caption = this.convertTextElementToCaption(element);

      // 将文字元素属性转换为CaptionStyle格式
      const captionStyle = this.convertTextElementToCaptionStyle(element);

      // 获取元素的动画信息
      const animationInfo = this.extractAnimationInfo(element);

      // 创建ASS字幕文件，传递元素的透明度和动画信息
      const elementOpacity =
        (element.placement as any)?.opacity || element.properties?.opacity;
      const assFilePath = TextElementASSUtils.createTextElementASSFile(
        caption,
        captionStyle,
        canvasWidth,
        canvasHeight,
        elementOpacity,
        animationInfo
      );

      // 生成ASS字幕滤镜
      const assFilter = this.generateASSFilter(
        assFilePath,
        lastVideoLabel,
        index
      );

      // 添加到滤镜复合体
      filterComplex.push(assFilter);

      console.log(`文字元素 ${element.id} 已转换为ASS字幕: ${assFilePath}`);
      if (animationInfo) {
        console.log(`文字元素 ${element.id} 包含动画效果:`, animationInfo);
      }
    } catch (error) {
      console.error(`处理文字元素ASS失败:`, error);
      // 如果ASS处理失败，回退到原来的drawtext方式
      this.fallbackToDrawtext(
        filterComplex,
        element,
        index,
        canvasWidth,
        canvasHeight,
        duration,
        lastVideoLabel
      );
    }
  }

  /**
   * 将文字元素转换为Caption格式
   */
  private convertTextElementToCaption(element: MediaElement): Caption {
    const startTimeMs = element.timeFrame.start;
    const endTimeMs = element.timeFrame.end;

    // 转换时间格式为 HH:MM:SS
    const formatTime = (ms: number): string => {
      const totalSeconds = Math.floor(ms / 1000);
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    };

    return {
      id: element.id,
      text: (element.properties as any).text || "",
      startTime: formatTime(startTimeMs),
      endTime: formatTime(endTimeMs),
    };
  }

  /**
   * 将文字元素属性转换为CaptionStyle格式
   */
  private convertTextElementToCaptionStyle(
    element: MediaElement
  ): CaptionStyle {
    const props = element.properties as any;
    const placement = element.placement;

    // 根据textAlign设置originX
    let originX: "left" | "center" | "right" = "left";
    if (props.textAlign === "center") {
      originX = "center";
    } else if (props.textAlign === "right") {
      originX = "right";
    }

    return {
      fontSize: props.fontSize || 24,
      fontFamily: props.fontFamily || "Arial",
      fontColor: props.fontColor || "#ffffff",
      fontWeight: props.fontWeight || 400,
      textAlign: props.textAlign || "left",
      lineHeight: props.lineHeight || 1.2,
      charSpacing: props.charSpacing || 0,
      styles: props.styles || [],
      strokeWidth: props.strokeWidth || 0,
      strokeColor: props.strokeColor || "#000000",
      shadowColor: props.shadowColor || "#000000",
      shadowBlur: props.shadowBlur || 0,
      shadowOffsetX: props.shadowOffsetX || 0,
      shadowOffsetY: props.shadowOffsetY || 0,
      backgroundColor: props.backgroundColor || "transparent",
      useGradient: props.useGradient || false,
      gradientColors: props.gradientColors || ["#ffffff", "#000000"],
      // 位置信息从placement中获取
      positionX: placement?.x || 0,
      positionY: placement?.y || 0,
      // 尺寸信息从placement中获取（前端设置的文字框尺寸）
      width: placement?.width,
      height: placement?.height,
      // 透明度信息
      opacity:
        props.opacity !== undefined
          ? props.opacity
          : (placement as any)?.opacity !== undefined
          ? (placement as any).opacity
          : 1,
      // 缩放信息（用于处理canvas控制框缩放）
      scaleX: placement?.scaleX || 1,
      scaleY: placement?.scaleY || 1,
      originX: originX,
      originY: "top", // 文字元素使用top作为垂直原点
    };
  }

  /**
   * 从元素中提取动画信息
   * 解析transition属性，转换为ASS动画标签所需的格式
   */
  private extractAnimationInfo(element: MediaElement): any {
    const transition = (element as any).transition;
    if (!transition) {
      return null;
    }

    const animationInfo: any = {};

    // 处理入场动画
    if (transition.in && transition.in !== "none") {
      animationInfo.fadeIn = this.convertTransitionToASSAnimation(
        transition.in,
        transition.inDuration || transition.duration || 1,
        "in"
      );
    }

    // 处理出场动画
    if (transition.out && transition.out !== "none") {
      animationInfo.fadeOut = this.convertTransitionToASSAnimation(
        transition.out,
        transition.outDuration || transition.duration || 1,
        "out"
      );
    }

    return Object.keys(animationInfo).length > 0 ? animationInfo : null;
  }

  /**
   * 将transition类型转换为ASS动画标签
   * 完全对应旧的drawtext方式使用的xfade滤镜效果
   */
  private convertTransitionToASSAnimation(
    transitionType: string,
    duration: number,
    direction: "in" | "out"
  ): any {
    const durationMs = Math.round(duration * 1000); // 转换为毫秒

    switch (transitionType) {
      case "fade":
        return {
          type: "fade",
          duration: durationMs,
          direction: direction,
        };

      // Slide动画效果 - 对应xfade的slide系列
      case "slideleft":
        return {
          type: "slide",
          duration: durationMs,
          direction: direction,
          slideDirection: "left", // 从左侧滑入/滑出
        };
      case "slideright":
        return {
          type: "slide",
          duration: durationMs,
          direction: direction,
          slideDirection: "right", // 从右侧滑入/滑出
        };
      case "slideup":
        return {
          type: "slide",
          duration: durationMs,
          direction: direction,
          slideDirection: "up", // 从上方滑入/滑出
        };
      case "slidedown":
        return {
          type: "slide",
          duration: durationMs,
          direction: direction,
          slideDirection: "down", // 从下方滑入/滑出
        };

      // Wipe动画效果 - 对应xfade的wipe系列
      case "wipeleft":
        return {
          type: "wipe",
          duration: durationMs,
          direction: direction,
          wipeDirection: "left", // 向左擦除
        };
      case "wiperight":
        return {
          type: "wipe",
          duration: durationMs,
          direction: direction,
          wipeDirection: "right", // 向右擦除
        };
      case "wipeup":
        return {
          type: "wipe",
          duration: durationMs,
          direction: direction,
          wipeDirection: "up", // 向上擦除
        };
      case "wipedown":
        return {
          type: "wipe",
          duration: durationMs,
          direction: direction,
          wipeDirection: "down", // 向下擦除
        };

      // 其他动画类型
      case "circleopen":
        return {
          type: "zoom",
          duration: durationMs,
          direction: direction,
        };

      default:
        // 对于不支持的动画类型，默认使用淡入淡出
        console.log(`不支持的动画类型: ${transitionType}，使用fade作为默认`);
        return {
          type: "fade",
          duration: durationMs,
          direction: direction,
        };
    }
  }

  /**
   * 生成ASS字幕滤镜
   */
  private generateASSFilter(
    assFilePath: string,
    lastVideoLabel: string,
    index: number
  ): string {
    const validVideoLabel = lastVideoLabel === `v-1` ? "bg" : lastVideoLabel;
    const escapedPath = assFilePath.replace(/\\/g, "\\\\").replace(/'/g, "\\'");

    return `[${validVideoLabel}]ass='${escapedPath}'[v${index}]`;
  }

  /**
   * 回退到drawtext方式（如果ASS处理失败）
   */
  private fallbackToDrawtext(
    filterComplex: string[],
    element: MediaElement,
    index: number,
    canvasWidth: number,
    canvasHeight: number,
    duration: number,
    lastVideoLabel: string
  ): void {
    console.log(`回退到drawtext方式处理文字元素 ${element.id}`);

    // 使用原来的文字处理方式
    this.applyOverlay(
      filterComplex,
      element,
      index,
      canvasWidth,
      canvasHeight,
      duration,
      "text",
      lastVideoLabel
    );
  }

  /**
   * 清理临时文件
   * 应在视频生成完成后调用
   */
  cleanup(): void {
    this.shapeFilterGenerator.cleanup();
  }

  /**
   * Apply media overlay effect to combine the element with the existing video stream
   *
   * @param filterComplex - Array of complex filters to be modified
   * @param element - The element to overlay
   * @param index - Current element index in the timeline
   * @param canvasWidth - Width of the output canvas in pixels
   * @param canvasHeight - Height of the output canvas in pixels
   * @param duration - Total duration of the output video
   * @param mediaType - Type of media being overlaid ('vid', 'img', or 'text')
   * @param lastVideoLabel - Label of the last processed video element
   */
  private applyOverlay(
    filterComplex: string[],
    element: MediaElement,
    index: number,
    canvasWidth: number,
    canvasHeight: number,
    duration: number,
    mediaType: string,
    lastVideoLabel: string = `v${index - 1}`
  ): void {
    // Ensure we have a valid lastVideoLabel, default to "bg" if not
    const validVideoLabel = lastVideoLabel === `v-1` ? "bg" : lastVideoLabel;

    filterComplex.push(
      ...this.mediaOverlayFilterGenerator.generateMediaOverlayFilters(
        element,
        index,
        canvasWidth,
        canvasHeight,
        duration,
        mediaType
      )
    );

    // 计算元素的显示时间范围（秒）
    const startTime = Number((element.timeFrame.start / 1000).toFixed(2));
    const endTime = Number((element.timeFrame.end / 1000).toFixed(2));

    // 添加enable参数来精确控制元素的显示时间
    const enableCondition = `enable='between(t,${startTime},${endTime})'`;

    filterComplex.push(
      `[${validVideoLabel}][${mediaType}_timed${index}]overlay=format=auto:${enableCondition},format=yuv420p[v${index}]`
    );
  }
}
