# 字体模板模块

## 文件结构

- `textTemplates.js` - 包含所有字体模板的配置数据
- `Texts.tsx` - 字体选择器组件的 UI 逻辑

## 字体模板配置

`textTemplates.js` 导出一个 `getTextResources` 函数，该函数接受一个翻译函数 `t` 并返回字体模板数组。

### 模板结构

每个字体模板包含以下属性：

```javascript
{
  name: "模板名称",
  fontSize: 36,                    // 字体大小
  fontWeight: 600,                 // 字体粗细
  fontFamily: "Arial",             // 字体族
  category: "title",               // 分类 (title, subtitle, body, annotation, social, creative, effects)
  color: "#ffffff",                // 文字颜色
  description: "模板描述",          // 描述信息
  shadowBlur: 0,                   // 阴影模糊度
  shadowOffsetX: 0,                // 阴影X偏移
  shadowOffsetY: 0,                // 阴影Y偏移
  shadowColor: "#000000",          // 阴影颜色
  strokeWidth: 0,                  // 描边宽度
  strokeColor: "#000000",          // 描边颜色
  backgroundColor: undefined,       // 背景颜色
  textAlign: "center",             // 文本对齐方式
  lineHeight: 1.2,                 // 行高
  charSpacing: 0,                  // 字符间距
}
```

### 分类说明

- `title` - 标题类样式
- `subtitle` - 字幕类样式
- `body` - 正文类样式
- `annotation` - 标注类样式
- `social` - 社交媒体类样式
- `creative` - 创意字体类样式
- `effects` - 特效样式类样式

## 如何添加新模板

1. 在 `textTemplates.js` 的数组中添加新的模板对象
2. 确保使用支持的字体族（参考后端 `mapFontFamily` 函数）
3. 设置合适的分类和属性值

## 支持的字体

后端支持以下字体族：

- Arial, Helvetica, Times New Roman, Georgia, Verdana
- Courier New, Impact, Comic Sans MS, Trebuchet MS, Arial Black
- Palatino, Garamond, Bookman, Avant Garde
- Roboto, Open Sans, Lato, Montserrat, Source Sans Pro
- Oswald, Raleway, PT Sans, Ubuntu, Merriweather, Playfair Display
