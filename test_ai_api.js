// 简单的AI API测试脚本
const fetch = require("node-fetch");

async function testAiApi() {
  const testData = {
    userInput: "帮我分析一下当前项目",
    canvasState: {
      width: 1280,
      height: 720,
      backgroundColor: "#111111",
      elements: [
        {
          id: "test1",
          type: "text",
          properties: { text: "Hello World" },
          timeFrame: { start: 0, end: 5000 },
          placement: { x: 100, y: 100, width: 200, height: 50 },
        },
        {
          id: "test2",
          type: "image",
          properties: { src: "test.jpg" },
          timeFrame: { start: 1000, end: 6000 },
          placement: { x: 200, y: 200, width: 300, height: 200 },
        },
      ],
      animations: [],
      captions: [],
      tracks: [],
    },
  };

  try {
    console.log("🚀 测试AI API...");

    const response = await fetch("http://localhost:8080/api/ai/chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testData),
    });

    console.log("📡 响应状态:", response.status);

    const result = await response.json();

    if (response.ok) {
      console.log("✅ API调用成功!");
      console.log("🤖 AI响应:", result.response);
      console.log("📊 元数据:", result.metadata);
    } else {
      console.log("❌ API调用失败:");
      console.log("错误:", result.error);
      console.log("详情:", result.details);
    }
  } catch (error) {
    console.log("💥 请求失败:", error.message);
  }
}

// 运行测试
testAiApi();
